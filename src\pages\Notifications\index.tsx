import "./styles.scss";
import { useNotificationsQuery, useReadNotificationMutation } from "api";
import { useEffect, useMemo, useState } from "react";
import { fromTimestampToDate } from "utils";
import { setUserInfo } from "stores";
import useUserStore from "stores/user";
import { NotificationTable } from "components";

const Notifications = () => {
  const [paginationConfig, setPaginationConfig] = useState({
    page: 1,
    limit: 25,
  });
  const [isRowHover, setIsRowHover] = useState<any>({});
  const userInfo = useUserStore((state) => state.userInfo);

  const {
    data: {
      data: notificationData,
      count: totalCount,
      has_unread_notification,
    } = {},
    refetch,
  } = useNotificationsQuery({ params: paginationConfig });
  const { mutateAsync: readNotification } = useReadNotificationMutation();

  const columns = [
    {
      field: "title",
      headerName: "Title",
    },
    {
      field: "message",
      headerName: "Message",
    },
    {
      field: "created_at",
      headerName: "Date",
    },
  ];

  const rows = useMemo(() => {
    if (notificationData?.length) {
      return notificationData.map((notificationItem: any) => ({
        ...notificationItem,
        created_at: fromTimestampToDate(notificationItem?.created_at),
      }));
    }
  }, [notificationData]);

  useEffect(() => {
    refetch();
  }, [paginationConfig?.limit, paginationConfig?.page, refetch]);

  useEffect(() => {
    setUserInfo({
      ...userInfo,
      user: {
        ...userInfo?.user,
        has_unread_notification,
      },
    });
  }, [has_unread_notification]);

  const onReadNotification = async (id: string | number) => {
    try {
      const result: any = await readNotification({ id });
      if (result?.success) {
        setUserInfo({
          ...userInfo,
          user: {
            ...userInfo?.user,
            has_unread_notification: result?.data?.has_unread_notification,
          },
        });
        refetch();
      }
    } catch (err: any) {
      console.log(err);
    }
  };

  return (
    <main className="notification-wrapper d-flex flex-column align-items-stretch justify-content-start bg-white">
      <NotificationTable
        columns={columns}
        rows={rows || []}
        paginationProps={{
          paginationConfig,
          setPaginationConfig,
          totalCount,
        }}
        isRowHover={isRowHover}
        setIsRowHover={setIsRowHover}
        onReadNotification={onReadNotification}
      />
    </main>
  );
};

export default Notifications;
