import { useMessageHistoryQuery } from "api";
import { useEffect, useLayoutEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import useChatStore, { setIsDocDeleted, setMessageHistory } from "stores/chat";
import { validate as isValidUUID } from "uuid";
import ChatInput from "../ChatInput";
import PrivacyOffcanvas from "../PrivacyOffcanvas";
import BoxHeader from "./BoxHeader";
import MessageHistory from "./MessageHistory";
import "./styles.scss";
import WelcomeScreen from "./WelcomeScreen";

const MessagesBox = () => {
  const { id: chat_id } = useParams();

  const [show, setShow] = useState(false);
  const [containerStyle, setContainerStyle] = useState<React.CSSProperties>({});

  const { messageHistory, isDocDeleted } = useChatStore((state) => state);
  const navigate = useNavigate();

  const filterRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const {
    data: { data: messagesData = [] } = {},
    refetch,
    isSuccess,
  } = useMessageHistoryQuery({
    chat_id,
  });

  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  const updateContainerStyle = () => {
    if (filterRef.current && inputRef.current) {
      const filterHeight = filterRef.current.offsetHeight;
      const inputHeight = inputRef.current.offsetHeight;

      const containerPaddingTop = 0;
      const containerPaddingBottom = 0;

      const messagesHeight = `calc(100% - ${filterHeight + containerPaddingTop + inputHeight + containerPaddingBottom + 15}px)`;

      setContainerStyle({
        height: messagesHeight,
        overflowY: "auto",
      });
    }
  };

  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  useLayoutEffect(() => {
    updateContainerStyle();

    window.addEventListener("resize", updateContainerStyle);

    const resizeObserver = new ResizeObserver(updateContainerStyle);
    if (filterRef.current) resizeObserver.observe(filterRef.current);
    if (inputRef.current) resizeObserver.observe(inputRef.current);

    return () => {
      window.removeEventListener("resize", updateContainerStyle);
      resizeObserver.disconnect();
    };
  }, []);

  useEffect(() => {
    if (isSuccess && messagesData?.length && chat_id) {
      setMessageHistory(messagesData);
      setIsDocDeleted(false);
    }
  }, [messagesData, chat_id, isSuccess]);

  useEffect(() => {
    if (!isDocDeleted) {
      scrollToBottom();
    }
  }, [messagesData, messageHistory, chat_id, isSuccess, isDocDeleted]);

  useEffect(() => {
    if (chat_id && !isValidUUID(chat_id as string)) {
      navigate(ROUTE_PATH.HOME);
    }
    if (chat_id) {
      refetch();
    } else {
      setMessageHistory([]);
    }
  }, [chat_id]);

  return (
    <>
      <div className="message-box bg-white h-100">
        <div className="message-box-chat-wrapper h-100 position-relative d-flex flex-column">
          <BoxHeader filterRef={filterRef} handleShow={handleShow} />
          {chat_id ? (
            <MessageHistory
              containerStyle={containerStyle}
              messageHistory={messageHistory}
              messagesEndRef={messagesEndRef}
              setIsDocDeleted={setIsDocDeleted}
            />
          ) : (
            <WelcomeScreen containerStyle={containerStyle} />
          )}

          <div
            ref={inputRef}
            className="message-box-chat-wrapper-input bg-white position-absolute"
          >
            <ChatInput messageHistory={messageHistory} />

            {/* <Form className="m-0 p-0">
              <Form.Check
                className="m-0"
                label="Enable Privacy Mode"
                type="switch"
              />
            </Form> */}
          </div>
        </div>
      </div>

      <PrivacyOffcanvas show={show} handleClose={handleClose} />
    </>
  );
};

export default MessagesBox;
