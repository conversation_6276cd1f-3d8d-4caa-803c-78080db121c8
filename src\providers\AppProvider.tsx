import { ConfirmModal } from "components";
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from "react-router-dom";
import { Auth0SSOProvider } from "./Auth0SSOProvider";
import { ReactQueryProvider } from "./ReactQueryProvider";
import { RoutingProvider } from "./RoutingProvider";
import ToastProvider from "./ToastProvider";

export const AppProvider = () => {
  return (
    // <Auth0SSOProvider>
      <ReactQueryProvider>
        <BrowserRouter
          basename={import.meta.env.VITE_BASE_URL}
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true,
          }}
        >
          <RoutingProvider />
          <ToastProvider />
          <ConfirmModal />
        </BrowserRouter>
      </ReactQueryProvider>
    // </Auth0SSOProvider>
  );
};
