import react from "@vitejs/plugin-react-swc";
import legacy from "@vitejs/plugin-legacy";
import { defineConfig, loadEnv } from "vite";
import { nodePolyfills } from "vite-plugin-node-polyfills";

export default defineConfig(() => {
  const env = loadEnv("env", process.cwd());
  return {
    plugins: [
      react(),
      legacy({
        targets: ["defaults", "chrome >= 64", "safari >= 12"],
        modernPolyfills: ["es.object.has-own"],
      }),
      nodePolyfills(),
    ],
    css: {
      devSourcemap: true, // Show SCSS file names in the browser's inspect section
      preprocessorOptions: {
        scss: {
          api: "modern-compiler",
        },
      },
    },
    server: {
      port: 3000, // Default port
      open: true, // Open the website in the default browser when the server starts
      host: true,
      watch: {
        usePolling: true, // Use polling to watch for file changes
      },
    },
    resolve: {
      // Aliases for directories
      alias: {
        api: "/src/api",
        assets: "/src/assets",
        components: "/src/components",
        globals: "/src/globals",
        formSchema: "/src/formSchema",
        hooks: "/src/hooks",
        layouts: "/src/layouts",
        pages: "/src/pages",
        providers: "/src/providers",
        routes: "/src/routes",
        stores: "/src/stores",
        styles: "/src/styles",
        utils: "/src/utils",
        features: "/src/features",
        types: "/src/types",
      },
    },
    build: {
      outDir: "./build", // Change build folder name dist to build
      chunkSizeWarningLimit: 1600,
    },
    base: env.VITE_BASE_URL, // Default base url
  };
});
