import { RiCloseLine } from "@remixicon/react";
import { useTranscription } from "hooks";
import { useEffect, useMemo, useRef, useState } from "react";
import { But<PERSON>, Modal } from "react-bootstrap";
import EditMode from "./EditMode";
import RecordMode from "./RecordMode";
import "./styles.scss";

interface DictateModalProps {
  show: boolean;
  onClose: () => void;
  onFinish: (text: string) => void;
}

// Wake Lock API types for preventing device sleep during recording
interface WakeLockSentinel {
  release: () => Promise<void>;
}

export default function DictateModal({
  show,
  onClose,
  onFinish,
}: DictateModalProps) {
  const textareaRef = useRef<HTMLTextAreaElement | null>(null);
  const cursorPositionRef = useRef<number | null>(null);
  const wakeLockRef = useRef<WakeLockSentinel | null>(null);

  const {
    isRecording,
    transcriptions,
    startRecording,
    stopRecording,
    stopAndClearRecording,
    FINISH_DELAY_TIME,
    error,
  } = useTranscription({ textareaRef, type: "report_dictation" });

  const [view, setView] = useState<"record" | "edit">("record");
  const [text, setText] = useState<string>("");
  const [hasStarted, setHasStarted] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Wake lock functions for preventing device sleep during recording
  const requestWakeLock = async () => {
    try {
      if ('wakeLock' in navigator) {
        wakeLockRef.current = await (navigator as any).wakeLock.request('screen');
        console.log('Wake lock acquired');
      }
    } catch (error) {
      console.warn('Wake lock failed:', error);
    }
  };

  const releaseWakeLock = async () => {
    if (wakeLockRef.current) {
      try {
        await wakeLockRef.current.release();
        wakeLockRef.current = null;
        console.log('Wake lock released');
      } catch (error) {
        console.warn('Wake lock release failed:', error);
      }
    }
  };

  useEffect(() => {
    setText(transcriptions || "");
    if (transcriptions && transcriptions.trim().length > 0) {
      setHasStarted(true);
    }
  }, [transcriptions]);

  useEffect(() => {
    if (!show) {
      // Immediate cleanup when modal is closed
      setIsLoading(false);
      setTimeout(async () => {
        setView("record");
        setText("");
        setHasStarted(false);
        stopAndClearRecording();
        await releaseWakeLock();
      }, 0);
    } else {
      // Reset state when modal is opened
      setIsLoading(false);
      setView("record");
      setText("");
      setHasStarted(false);
    }
  }, [show, stopAndClearRecording]);

  // Monitor recording state changes
  useEffect(() => {
    if (!isRecording && wakeLockRef.current) {
      // Release wake lock when recording stops
      releaseWakeLock();
    }
  }, [isRecording]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      releaseWakeLock();
    };
  }, []);

  const handleToggleRecording = async () => {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      alert("Microphone not supported in this browser.");
      return;
    }

    try {
      if (isRecording) {
        stopRecording();
        await releaseWakeLock();
      } else {
        setIsLoading(true);
        // Request wake lock to prevent device sleep
        await requestWakeLock();
        // Now start the actual recording
        await startRecording();
        setHasStarted(true);
      }
    } catch (error: any) {
      console.error("Recording error:", error);

      let errorMessage = "Please grant microphone permissions to use this feature.";

      if (error.name === "NotAllowedError") {
        errorMessage = "Microphone access denied. Please allow microphone access in your browser settings and try again.";
      } else if (error.name === "NotFoundError") {
        errorMessage = "No microphone found. Please connect a microphone and try again.";
      } else if (error.name === "NotReadableError") {
        errorMessage = "Microphone is being used by another application. Please close other apps and try again.";
      } else if (error.name === "OverconstrainedError") {
        errorMessage = "Microphone doesn't support the required settings. Please try again.";
      } else if (error.name === "AbortError") {
        errorMessage = "Recording was interrupted. Please try again.";
      } else if (error.name === "SecurityError") {
        errorMessage = "Recording not allowed due to security restrictions. Please ensure you're using HTTPS.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Show error in modal instead of alert for better UX
      console.error("Recording error:", errorMessage);
      await releaseWakeLock();
    } finally {
      setIsLoading(false);
    }
  };

  const handleFinishRecording = async () => {
    try {
      setIsLoading(true);
      if (isRecording) {
        stopRecording({ isFinished: true });
      }
      await releaseWakeLock();

      const delay = Math.min(FINISH_DELAY_TIME || 2000, 1000); // Reduce delay for better UX
      setTimeout(() => {
        setIsLoading(false);
      }, delay);
      setTimeout(() => setView("edit"), delay);
    } catch (error) {
      console.error("Error finishing recording:", error);
      setIsLoading(false);
    }
  };

  const handleReset = async () => {
    try {
      setIsLoading(true);
      stopAndClearRecording();
      setText("");
      setHasStarted(false);
      await releaseWakeLock();
    } catch (error) {
      console.error("Error during reset:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFinalFinish = () => {
    const finalText = textareaRef.current?.value ?? text;
    onFinish((finalText || "").trim());
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const textarea = textareaRef.current;
    if (textarea) {
      cursorPositionRef.current = textarea.selectionStart;
    }
    setText(e.target.value);
  };

  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea && cursorPositionRef.current !== null) {
      textarea.selectionStart = cursorPositionRef.current;
      textarea.selectionEnd = cursorPositionRef.current;
    }
  }, [text]);

  const formattedDateTime = useMemo(() => {
    const now = new Date();
    const date = now.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    });
    const time = now.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
    return `${date} | ${time}`;
  }, [show]);

  const hasAnyText = (text ?? "").trim().length > 0;

  return (
    <Modal
      show={show}
      keyboard={false}
      centered
      className="dictate-modal"
      dialogClassName={`dictate-${view}`}
    >
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative p-4">
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={onClose}
        >
          <RiCloseLine size={40} color="#f9f9f9" />
        </Button>

        {view === "record" ? (
          <RecordMode
            config={{
              isRecording,
              handleToggleRecording,
              hasStarted,
              handleReset,
              handleFinishRecording,
              hasAnyText,
              isLoading,
              error,
            }}
          />
        ) : (
          <EditMode
            config={{
              formattedDateTime,
              textareaRef,
              text,
              handleTextChange,
              handleFinalFinish,
              hasAnyText,
            }}
          />
        )}
      </Modal.Body>
    </Modal>
  );
}
