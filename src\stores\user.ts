import { OrgUserRole } from "globals";
import { UserInterface } from "types";
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

interface UserInfo {
  user: UserInterface | null;
  token: string | null;
  loginType: string | null;
}

interface PayloadInterface {
  userInfo: UserInfo;
  rememberMeInfo?: Record<string, string>;
}

const initialState = {
  // userInfo: {
  //   user: {} as UserInterface,
  //   token: "",
  //   loginType: null,
  // },
  // rememberMeInfo: {
  //   email: "",
  // },
  // subscriptionInfo: {},
  "userInfo": {
    "token": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "loginType": "embedded",
    "user": {
      "id": "1350",
      "first_name": "Nick",
      "last_name": "Wilson",
      "full_name": "Nick Wilson",
      "email": "<EMAIL>",
      "email_verified": true,
      "status": "active",
      "profile_photo": "",
      "redact_setting": {
        "enable_privacy": false,
        "preset": [
          {
            "entity_values": [
              "PHONE_NUMBER",
              "PERSON",
              "EMAIL_ADDRESS",
              "IP_ADDRESS",
              "TITLE",
              "UK_NI",
              "BANK_ACCOUNT_NUMBER",
              "BANK_SORT_CODE",
              "IBAN_CODE",
              "SWIFT_CODE",
              "CREDIT_CARD_NUMBER",
              "ORGANIZATION",
              "ZIP_CODE",
              "LOCATION"
            ],
            "type": "custom1"
          }
        ]
      },
      "email_verified_at": "2025-04-21T09:46:11.770Z",
      "parent_id": null,
      "stripe_customer_id": "",
      "branding": null,
      "session_id": "e70827c44189a011c017af1d0de0c859",
      "is_offline_account": true,
      "offline_billing_setting": {
        "term": 60,
        "email": "<EMAIL>",
        "amount": 1000,
        "status": "active",
        "licenses": 5,
        "word_limit": 50000,
        "monthly_fee": 500,
        "account_name": "Nick Wilson",
        "invoice_date": "2025-04-24",
        "invoice_number": "7B574A7C- 0005",
        "account_address": "Jaipur, Rajasthan, India",
        "activation_token": "",
        "terms_sheet_file": "wealthspace/users/offline-billing/0c5b3db9-6e03-4552-8506-725c90d72dcf.pdf",
        "terms_accepted_at": "2025-04-21T06:37:18.170Z",
        "terms_sheet_status": true,
        "account_lead_contact": "Nick Wilson",
        "account_contact_email": "<EMAIL>",
        "account_contact_number": "**********",
        "account_acknowledge_token": ""
      },
      "export_settings": null,
      "userRoles": [],
      "ownedOrganization": {
        "id": 186,
        "user_id": 1350,
        "title": "Nick Org",
        "logo": "",
        "settings": {
          "basic_filters": false,
          "all_filters": false,
          "show_logo": false,
          "delete_chat_history": false,
          "export_chat_history": false,
          "show_prompt": false,
          "export_settings": false,
          "disable_default_prompts": false
        },
        "status": "active",
        "prompts": [
          {
            "id": 83,
            "organization_id": 186,
            "title": "Test 2",
            "prompt": "hello world hello world hello world hello world hello world hello world",
            "template": null,
            "icon": "email",
            "metadata": null,
            "created_at": "2025-07-29T12:25:34.565Z",
            "updated_at": "2025-07-29T12:25:34.565Z"
          }
        ]
      },
      "organizationMember": {
        "id": 340,
        "organization_id": 186,
        "member_id": 1350,
        "role": "admin",
        "status": "active",
        "organization": {
          "id": 186,
          "title": "Nick Org",
          "logo": "",
          "settings": {
            "basic_filters": false,
            "all_filters": false,
            "show_logo": false,
            "delete_chat_history": false,
            "export_chat_history": false,
            "show_prompt": false,
            "export_settings": false,
            "disable_default_prompts": false
          },
          "export_settings": null,
          "prompts": [
            {
              "id": 83,
              "organization_id": 186,
              "title": "Test 2",
              "prompt": "hello world hello world hello world hello world hello world hello world",
              "template": null,
              "icon": "email",
              "metadata": null,
              "created_at": "2025-07-29T12:25:34.565Z",
              "updated_at": "2025-07-29T12:25:34.565Z"
            }
          ]
        }
      },
      "superadmin_offline_access": false,
      "has_unread_notification": false,
      "terms_sheet_url": "https://konstantdemo2.s3.us-east-1.amazonaws.com/wealthspace/users/offline-billing/0c5b3db9-6e03-4552-8506-725c90d72dcf.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIA6M5ZYKERZGGP3W4D%2F20250826%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250826T071748Z&X-Amz-Expires=3600&X-Amz-Signature=3608b564969d0c07bfe0eb8e7036795f91e882475b4dd7f63d626da56c43741c&X-Amz-SignedHeaders=host&x-id=GetObject",
      "is_subscription": true
    }
  },
  "rememberMeInfo": {
    "email": ""
  },
  "subscriptionInfo": {
    "is_tier": true,
    "features": {
      "offline": true,
      "reports": true,
      "organization": true,
      "knowledgebase": true
    }
  }
};

const userStore = (set: any) => ({
  ...initialState,
  setUserInfo: (data: UserInfo) =>
    set((state: PayloadInterface) => ({ ...state, userInfo: data })),
  setRememberMeInfo: (data: Record<string, string>) =>
    set((state: PayloadInterface) => ({ ...state, rememberMeInfo: data })),
  setOrganisation: (data: any) =>
    set((state: PayloadInterface) => {
      if (!state.userInfo.user) {
        return { userInfo: state.userInfo };
      }

      const isAdmin =
        state.userInfo.user.organizationMember?.role === OrgUserRole.ADMIN;
      const isUser =
        state.userInfo.user.organizationMember?.role === OrgUserRole.USER;
      const updatedUser = {
        ...state.userInfo.user,
        ...(isAdmin || isUser
          ? {
              organizationMember: {
                ...state.userInfo.user.organizationMember,
                organization: data,
              },
            }
          : { ownedOrganization: data }),
      };

      return {
        userInfo: {
          ...state.userInfo,
          user: updatedUser,
        },
      };
    }),
  setOrganisationMember: (data: any) =>
    set((state: PayloadInterface) => {
      if (!state.userInfo.user) {
        return { userInfo: state.userInfo };
      }
      const updatedUser = {
        ...state.userInfo.user,
        ...(state.userInfo.user.organizationMember
          ? {
              organizationMember: {
                ...state.userInfo.user.organizationMember,
                ...data,
              },
            }
          : { organizationMember: data }),
      };

      return {
        userInfo: {
          ...state.userInfo,
          user: updatedUser,
        },
      };
    }),
  setSubscriptionInfo: (data: any) =>
    set((state: PayloadInterface) => ({
      ...state,
      subscriptionInfo: data,
    })),
  resetUserState: () =>
    set((state: PayloadInterface) => ({
      ...initialState,
      rememberMeInfo: state.rememberMeInfo,
    })),
});

const useUserStore = create(
  devtools(
    persist(userStore, {
      name: "user",
    })
  )
);

export const getAuthToken = () => {
  // access the zustand store outside of React.
  return useUserStore.getState().userInfo.token;
};

export const getOrganisationInfo = (): any => {
  const user = useUserStore.getState().userInfo.user;
  const isOrgAdmin = user.organizationMember?.role === OrgUserRole.ADMIN;
  const isOrgUser = user.organizationMember?.role === OrgUserRole.USER;

  return isOrgAdmin || isOrgUser
    ? user.organizationMember?.organization
    : user.ownedOrganization;
};

export const {
  setUserInfo,
  resetUserState,
  setRememberMeInfo,
  setOrganisation,
  setSubscriptionInfo,
  setOrganisationMember,
} = useUserStore.getState();

export default useUserStore;
