import { OrgUserRole } from "globals";
import { UserInterface } from "types";
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

interface UserInfo {
  user: UserInterface | null;
  token: string | null;
  loginType: string | null;
}

interface PayloadInterface {
  userInfo: UserInfo;
  rememberMeInfo?: Record<string, string>;
}

const initialState = {
  // userInfo: {
  //   user: {} as UserInterface,
  //   token: "",
  //   loginType: null,
  // },
  // rememberMeInfo: {
  //   email: "",
  // },
  // subscriptionInfo: {},
  userInfo: {
    token:
      "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    loginType: "embedded",
    user: {
      id: "939",
      first_name: "Baron",
      last_name: "John",
      full_name: "Baron John",
      email: "<EMAIL>",
      email_verified: true,
      status: "active",
      profile_photo: "",
      redact_setting: null,
      email_verified_at: "2025-02-13T11:03:35.472Z",
      parent_id: null,
      stripe_customer_id: "cus_RlXHsdkTGyXbB1",
      branding: null,
      session_id: "37f53c13fefaa68b9fb597f46cfb5ef4",
      is_offline_account: false,
      offline_billing_setting: null,
      export_settings: null,
      userRoles: [
        {
          id: 878,
          user_id: "939",
          role_id: 1,
          role: {
            id: 1,
            auth0_id: "rol_c1kJjYrsHMH39NKr",
            name: "Chatredact",
          },
        },
      ],
      ownedOrganization: {
        id: 189,
        user_id: 939,
        title: "Baron Org",
        logo: "",
        settings: {
          basic_filters: false,
          all_filters: false,
          show_logo: false,
          delete_chat_history: false,
          export_chat_history: false,
          show_prompt: false,
          export_settings: false,
          disable_default_prompts: false,
        },
        status: "active",
        prompts: [],
      },
      organizationMember: {
        id: 344,
        organization_id: 189,
        member_id: 939,
        role: "admin",
        status: "active",
        organization: {
          id: 189,
          title: "Baron Org",
          logo: "",
          settings: {
            basic_filters: false,
            all_filters: false,
            show_logo: false,
            delete_chat_history: false,
            export_chat_history: false,
            show_prompt: false,
            export_settings: false,
            disable_default_prompts: false,
          },
          export_settings: null,
          prompts: [],
        },
      },
      superadmin_offline_access: false,
      has_unread_notification: false,
      is_subscription: true,
    },
  },
  rememberMeInfo: {
    email: "",
  },
  subscriptionInfo: {
    is_tier: true,
    features: {
      offline: true,
      reports: true,
      organization: true,
      knowledgebase: true,
    },
  },
};

const userStore = (set: any) => ({
  ...initialState,
  setUserInfo: (data: UserInfo) =>
    set((state: PayloadInterface) => ({ ...state, userInfo: data })),
  setRememberMeInfo: (data: Record<string, string>) =>
    set((state: PayloadInterface) => ({ ...state, rememberMeInfo: data })),
  setOrganisation: (data: any) =>
    set((state: PayloadInterface) => {
      if (!state.userInfo.user) {
        return { userInfo: state.userInfo };
      }

      const isAdmin =
        state.userInfo.user.organizationMember?.role === OrgUserRole.ADMIN;
      const isUser =
        state.userInfo.user.organizationMember?.role === OrgUserRole.USER;
      const updatedUser = {
        ...state.userInfo.user,
        ...(isAdmin || isUser
          ? {
              organizationMember: {
                ...state.userInfo.user.organizationMember,
                organization: data,
              },
            }
          : { ownedOrganization: data }),
      };

      return {
        userInfo: {
          ...state.userInfo,
          user: updatedUser,
        },
      };
    }),
  setOrganisationMember: (data: any) =>
    set((state: PayloadInterface) => {
      if (!state.userInfo.user) {
        return { userInfo: state.userInfo };
      }
      const updatedUser = {
        ...state.userInfo.user,
        ...(state.userInfo.user.organizationMember
          ? {
              organizationMember: {
                ...state.userInfo.user.organizationMember,
                ...data,
              },
            }
          : { organizationMember: data }),
      };

      return {
        userInfo: {
          ...state.userInfo,
          user: updatedUser,
        },
      };
    }),
  setSubscriptionInfo: (data: any) =>
    set((state: PayloadInterface) => ({
      ...state,
      subscriptionInfo: data,
    })),
  resetUserState: () =>
    set((state: PayloadInterface) => ({
      ...initialState,
      rememberMeInfo: state.rememberMeInfo,
    })),
});

const useUserStore = create(
  devtools(
    persist(userStore, {
      name: "user",
    })
  )
);

export const getAuthToken = () => {
  // access the zustand store outside of React.
  return useUserStore.getState().userInfo.token;
};

export const getOrganisationInfo = (): any => {
  const user = useUserStore.getState().userInfo.user;
  const isOrgAdmin = user.organizationMember?.role === OrgUserRole.ADMIN;
  const isOrgUser = user.organizationMember?.role === OrgUserRole.USER;

  return isOrgAdmin || isOrgUser
    ? user.organizationMember?.organization
    : user.ownedOrganization;
};

export const {
  setUserInfo,
  resetUserState,
  setRememberMeInfo,
  setOrganisation,
  setSubscriptionInfo,
  setOrganisationMember,
} = useUserStore.getState();

export default useUserStore;
