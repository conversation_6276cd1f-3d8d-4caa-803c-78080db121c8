import { <PERSON><PERSON><PERSON> } from "buffer";
import MicrophoneStream from "microphone-stream";
import { useCallback, useEffect, useRef, useState } from "react";
import { io, Socket } from "socket.io-client";

const WS_ENDPOINT = import.meta.env.VITE_WEBSOCKET_URL;
const FINISH_DELAY_TIME = 2000;

type TranscriptionCallback = (data: {
  text: string;
  speaker?: string;
  isFinal?: boolean;
}) => void;

interface useTranscriptionInterface {
  isRecording: boolean;
  transcriptions: string;
  startRecording: () => Promise<void>;
  stopRecording: (options?: { isFinished?: boolean }) => void;
  error?: string;
  stopAndClearRecording: () => void;
  FINISH_DELAY_TIME?: number;
}

const useTranscription = ({
  textareaRef,
  type = "transcription",
}: {
  textareaRef: React.MutableRefObject<HTMLTextAreaElement | null>;
  type: "report_dictation" | "transcription";
}): useTranscriptionInterface => {
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [transcriptions, setTranscriptions] = useState<string>("");
  const [error, setError] = useState<string | undefined>(undefined);

  let microphoneStream: any;
  let socket: Socket | null = null;
  let currentTranscript = "";

  const stopTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const clearStopTimeout = () => {
    if (stopTimeoutRef.current) {
      clearTimeout(stopTimeoutRef.current);
      stopTimeoutRef.current = null;
    }
  };

  const encodePCMChunk = (chunk: any): Buffer => {
    const input = MicrophoneStream.toRaw(chunk) as Float32Array;
    const buffer = new ArrayBuffer(input.length * 2);
    const view = new DataView(buffer);
    let offset = 0;

    for (let i = 0; i < input.length; i++, offset += 2) {
      const s = Math.max(-1, Math.min(1, input[i]));
      view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
    }
    return Buffer.from(buffer);
  };

  const createMicrophoneStream = async (): Promise<void> => {
    microphoneStream = new MicrophoneStream();
    const stream = await navigator.mediaDevices.getUserMedia({
      video: false,
      audio: true,
    });
    microphoneStream.setStream(stream);
  };

  const handleTranscriptionData = useCallback<TranscriptionCallback>((data) => {
    if (data.isFinal) {
      currentTranscript += currentTranscript
        ? `\n\n${data.text} `
        : `${data.text} `;
    } else {
      const partialTranscript = `${currentTranscript} ${data.text}`;
      setTranscriptions(() => partialTranscript);
    }
    setTranscriptions(() => currentTranscript);
  }, []);

  const startRecording = useCallback(async (): Promise<void> => {
    setError(undefined);
    clearStopTimeout();
    setIsRecording(true);

    socket = io(WS_ENDPOINT);

    socket?.on("connect", async () => {
      await createMicrophoneStream();

      socket?.emit("startTranscription");

      microphoneStream.on("data", (chunk: any) => {
        socket?.emit("audioData", encodePCMChunk(chunk));
      });
    });

    socket?.on(
      "transcription",
      (data: { text: string; speaker?: string; isFinal?: boolean }) => {
        handleTranscriptionData(data);
      }
    );

    socket?.on("error", (errorMessage: string) => {
      console.error("Server error:", errorMessage);
      setError(errorMessage);
      stopRecording();
    });

    socket?.on("disconnect", () => {
      console.log("Socket disconnected");
      setIsRecording(false);
    });
  }, [handleTranscriptionData]);

  const stopRecording = useCallback(
    ({ isFinished }: { isFinished?: boolean } = {}) => {
      clearStopTimeout();

      stopTimeoutRef.current = setTimeout(() => {
        if (microphoneStream) {
          microphoneStream.stop();
          microphoneStream.destroy();
          microphoneStream = undefined;
        }

        if (socket) {
          socket.emit("stopTranscription");
          socket.disconnect();
        }

        if (
          type === "report_dictation" &&
          currentTranscript.trim().length > 0 &&
          !isFinished
        ) {
          currentTranscript += `\n\n<---------RECORDING PAUSED-------->`;
          setTranscriptions(currentTranscript);
        }
      }, FINISH_DELAY_TIME);

      setIsRecording(false);
    },
    []
  );

  const stopAndClearRecording = useCallback((): void => {
    stopRecording();
    currentTranscript = "";
    setTranscriptions("");
  }, []);

  useEffect(() => {
    return () => {
      // clearStopTimeout();
      stopRecording();
      socket?.disconnect();
    };
  }, [stopRecording]);

  useEffect(() => {
    textareaRef.current?.addEventListener("input", () => {
      currentTranscript = textareaRef.current?.value || "";
      setTranscriptions(currentTranscript);
    });

    return () => {
      textareaRef.current?.removeEventListener("input", () => {});
    };
  }, []);

  return {
    isRecording,
    transcriptions,
    startRecording,
    stopRecording,
    error,
    stopAndClearRecording,
    FINISH_DELAY_TIME,
  };
};

export default useTranscription;
