import { <PERSON><PERSON><PERSON> } from "buffer";
import MicrophoneStream from "microphone-stream";
import { useCallback, useEffect, useRef, useState } from "react";
import { io, Socket } from "socket.io-client";

const WS_ENDPOINT = import.meta.env.VITE_WEBSOCKET_URL;
const FINISH_DELAY_TIME = 2000;

// Detect iOS Safari
const isIOSSafari = () => {
  const ua = navigator.userAgent;
  const iOS = /iPad|iPhone|iPod/.test(ua);
  const webkit = /WebKit/.test(ua);
  const chrome = /CriOS|Chrome/.test(ua);
  return iOS && webkit && !chrome;
};

// Error handling utility
const getErrorMessage = (error: any): string => {
  if (error.name === "NotAllowedError") {
    return "Microphone access denied. Please allow microphone access in your browser settings and try again.";
  } else if (error.name === "NotFoundError") {
    return "No microphone found. Please connect a microphone and try again.";
  } else if (error.name === "NotReadableError") {
    return "Microphone is being used by another application. Please close other apps and try again.";
  } else if (error.name === "OverconstrainedError") {
    return "Microphone doesn't support the required settings. Please try again.";
  } else if (error.name === "AbortError") {
    return "Recording was interrupted. Please try again.";
  } else if (error.name === "SecurityError") {
    return "Recording not allowed due to security restrictions. Please ensure you're using HTTPS.";
  } else if (error.message) {
    return `Recording error: ${error.message}`;
  }
  return "An unexpected error occurred. Please try again.";
};

type TranscriptionCallback = (data: {
  text: string;
  speaker?: string;
  isFinal?: boolean;
}) => void;

interface useTranscriptionInterface {
  isRecording: boolean;
  transcriptions: string;
  startRecording: () => Promise<void>;
  stopRecording: (options?: { isFinished?: boolean }) => void;
  error?: string;
  stopAndClearRecording: () => void;
  FINISH_DELAY_TIME?: number;
}

const useTranscription = ({
  textareaRef,
  type = "transcription",
}: {
  textareaRef: React.MutableRefObject<HTMLTextAreaElement | null>;
  type: "report_dictation" | "transcription";
}): useTranscriptionInterface => {
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [transcriptions, setTranscriptions] = useState<string>("");
  const [error, setError] = useState<string | undefined>(undefined);

  // Use refs to maintain state across re-renders and avoid closure issues
  const microphoneStreamRef = useRef<any>(null);
  const socketRef = useRef<Socket | null>(null);
  const currentTranscriptRef = useRef<string>("");
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const isRecordingRef = useRef<boolean>(false);
  const stopTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const reconnectTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(
    null
  );
  const reconnectAttemptsRef = useRef<number>(0);
  const maxReconnectAttempts = 3;

  // Cleanup functions
  const clearAllTimeouts = useCallback(() => {
    if (stopTimeoutRef.current) {
      clearTimeout(stopTimeoutRef.current);
      stopTimeoutRef.current = null;
    }
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
  }, []);

  const cleanupMediaStream = useCallback(() => {
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach((track) => {
        track.stop();
      });
      mediaStreamRef.current = null;
    }
  }, []);

  const cleanupMicrophoneStream = useCallback(() => {
    if (microphoneStreamRef.current) {
      try {
        microphoneStreamRef.current.stop();
        microphoneStreamRef.current.destroy();
      } catch (error) {
        console.warn("Error cleaning up microphone stream:", error);
      }
      microphoneStreamRef.current = null;
    }
  }, []);

  const cleanupSocket = useCallback(() => {
    if (socketRef.current) {
      try {
        socketRef.current.emit("stopTranscription");
        socketRef.current.disconnect();
      } catch (error) {
        console.warn("Error cleaning up socket:", error);
      }
      socketRef.current = null;
    }
  }, []);

  const fullCleanup = useCallback(() => {
    clearAllTimeouts();
    cleanupMicrophoneStream();
    cleanupMediaStream();
    cleanupSocket();
    isRecordingRef.current = false;
    reconnectAttemptsRef.current = 0;
  }, [
    clearAllTimeouts,
    cleanupMicrophoneStream,
    cleanupMediaStream,
    cleanupSocket,
  ]);

  const encodePCMChunk = useCallback((chunk: any): Buffer => {
    const input = MicrophoneStream.toRaw(chunk) as Float32Array;
    const buffer = new ArrayBuffer(input.length * 2);
    const view = new DataView(buffer);
    let offset = 0;

    for (let i = 0; i < input.length; i++, offset += 2) {
      const s = Math.max(-1, Math.min(1, input[i]));
      view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
    }
    return Buffer.from(buffer);
  }, []);

  const createMicrophoneStream = useCallback(async (): Promise<MediaStream> => {
    try {
      // Clean up any existing streams first
      cleanupMicrophoneStream();
      cleanupMediaStream();

      // Request microphone access with iOS Safari optimizations
      const constraints: MediaStreamConstraints = {
        video: false,
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          // iOS Safari specific optimizations
          sampleRate: isIOSSafari() ? 16000 : 44100,
          channelCount: 1,
        },
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      mediaStreamRef.current = stream;

      // Create microphone stream
      microphoneStreamRef.current = new MicrophoneStream();
      microphoneStreamRef.current.setStream(stream);

      return stream;
    } catch (error) {
      console.error("Error creating microphone stream:", error);
      throw error;
    }
  }, [cleanupMicrophoneStream, cleanupMediaStream]);

  {
    /* Real time transcription */
  }
  // const handleTranscriptionData = useCallback<TranscriptionCallback>((data) => {
  //   if (data.isFinal) {
  //     currentTranscriptRef.current += currentTranscriptRef.current
  //       ? `\n\n${data.text} `
  //       : `${data.text} `;
  //     setTranscriptions(currentTranscriptRef.current);
  //   } else {
  //     const partialTranscript = `${currentTranscriptRef.current} ${data.text}`;
  //     setTranscriptions(partialTranscript);
  //   }
  // }, []);

  {
    /* Delay transcription */
  }
  const handleTranscriptionData = useCallback<TranscriptionCallback>((data) => {
    if (data.isFinal) {
      currentTranscriptRef.current += currentTranscriptRef.current
        ? `\n\n${data.text} `
        : `${data.text} `;
    } else {
      const partialTranscript = `${currentTranscriptRef.current} ${data.text}`;
      setTranscriptions(() => partialTranscript);
    }
    setTranscriptions(() => currentTranscriptRef.current);
  }, []);

  const connectSocket = useCallback(() => {
    return new Promise<Socket>((resolve, reject) => {
      const connectionTimeout = setTimeout(() => {
        reject(new Error("Connection timeout"));
      }, 15000); // 15 second timeout

      try {
        if (!WS_ENDPOINT) {
          reject(new Error("WebSocket endpoint not configured"));
          return;
        }

        const socket = io(WS_ENDPOINT, {
          transports: ["websocket", "polling"],
          timeout: 10000,
          forceNew: true,
          reconnection: false, // We handle reconnection manually
        });

        socket.on("connect", () => {
          console.log("Socket connected");
          clearTimeout(connectionTimeout);
          resolve(socket);
        });

        socket.on("connect_error", (error) => {
          console.error("Socket connection error:", error);
          clearTimeout(connectionTimeout);
          reject(
            new Error(`Connection failed: ${error.message || "Unknown error"}`)
          );
        });

        socket.on("error", (errorMessage: string) => {
          console.error("Server error:", errorMessage);
          setError(`Server error: ${errorMessage}`);
        });

        socket.on("disconnect", (reason) => {
          console.log("Socket disconnected:", reason);
          clearTimeout(connectionTimeout);

          if (isRecordingRef.current) {
            if (
              reason === "io server disconnect" ||
              reason === "transport close"
            ) {
              // Server disconnected or transport closed, try to reconnect
              console.log("Attempting to reconnect...");
              handleReconnect();
            } else {
              // Client initiated disconnect or other reasons
              setIsRecording(false);
              isRecordingRef.current = false;
            }
          }
        });

        socket.on(
          "transcription",
          (data: { text: string; speaker?: string; isFinal?: boolean }) => {
            try {
              handleTranscriptionData(data);
            } catch (error) {
              console.error("Error handling transcription data:", error);
            }
          }
        );
      } catch (error) {
        clearTimeout(connectionTimeout);
        reject(error);
      }
    });
  }, [handleTranscriptionData]);

  const handleReconnect = useCallback(() => {
    if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
      setError("Connection lost. Please try again.");
      setIsRecording(false);
      isRecordingRef.current = false;
      fullCleanup();
      return;
    }

    reconnectAttemptsRef.current++;
    reconnectTimeoutRef.current = setTimeout(async () => {
      if (isRecordingRef.current) {
        try {
          // Restart recording process
          setError(undefined);
          clearAllTimeouts();

          // Create microphone stream first
          await createMicrophoneStream();

          // Connect to socket
          const socket = await connectSocket();
          socketRef.current = socket;

          // Start transcription
          socket.emit("startTranscription");

          // Set up audio data streaming
          if (microphoneStreamRef.current) {
            microphoneStreamRef.current.on("data", (chunk: any) => {
              if (socketRef.current && isRecordingRef.current) {
                try {
                  socketRef.current.emit("audioData", encodePCMChunk(chunk));
                } catch (error) {
                  console.error("Error sending audio data:", error);
                }
              }
            });
          }
        } catch (error) {
          console.error("Reconnection failed:", error);
          setError("Reconnection failed. Please try again.");
          setIsRecording(false);
          isRecordingRef.current = false;
          fullCleanup();
        }
      }
    }, 1000 * reconnectAttemptsRef.current);
  }, [
    fullCleanup,
    clearAllTimeouts,
    createMicrophoneStream,
    connectSocket,
    encodePCMChunk,
  ]);

  const startRecording = useCallback(async (): Promise<void> => {
    try {
      setError(undefined);
      clearAllTimeouts();

      // Prevent multiple simultaneous recording attempts
      if (isRecordingRef.current) {
        console.warn("Recording already in progress");
        return;
      }

      // Check for basic browser support
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error("Microphone not supported in this browser");
      }

      isRecordingRef.current = true;
      setIsRecording(true);

      // Create microphone stream first with retry logic
      let retryCount = 0;
      const maxRetries = 3;
      let stream: MediaStream | null = null;

      while (retryCount < maxRetries && !stream) {
        try {
          stream = await createMicrophoneStream();
          break;
        } catch (error) {
          retryCount++;
          console.warn(
            `Microphone stream creation attempt ${retryCount} failed:`,
            error
          );

          if (retryCount >= maxRetries) {
            throw error;
          }

          // Wait before retry (exponential backoff)
          await new Promise((resolve) =>
            setTimeout(resolve, 1000 * retryCount)
          );
        }
      }

      // Connect to socket with retry logic
      let socket: Socket | null = null;
      retryCount = 0;

      while (retryCount < maxRetries && !socket) {
        try {
          socket = await connectSocket();
          socketRef.current = socket;
          break;
        } catch (error) {
          retryCount++;
          console.warn(
            `Socket connection attempt ${retryCount} failed:`,
            error
          );

          if (retryCount >= maxRetries) {
            throw new Error("Failed to connect to transcription service");
          }

          // Wait before retry
          await new Promise((resolve) =>
            setTimeout(resolve, 1000 * retryCount)
          );
        }
      }

      if (!socket) {
        throw new Error(
          "Failed to establish connection to transcription service"
        );
      }

      // Start transcription
      socket.emit("startTranscription");

      // Set up audio data streaming with error handling
      if (microphoneStreamRef.current) {
        microphoneStreamRef.current.on("data", (chunk: any) => {
          if (socketRef.current && isRecordingRef.current) {
            try {
              socketRef.current.emit("audioData", encodePCMChunk(chunk));
            } catch (error) {
              console.error("Error sending audio data:", error);
              // Don't throw here, just log the error to avoid interrupting the stream
            }
          }
        });

        // Handle microphone stream errors
        microphoneStreamRef.current.on("error", (error: any) => {
          console.error("Microphone stream error:", error);
          setError(getErrorMessage(error));
          // Stop recording by setting state and cleaning up
          isRecordingRef.current = false;
          setIsRecording(false);
          fullCleanup();
        });
      }

      // Reset reconnection attempts on successful start
      reconnectAttemptsRef.current = 0;
    } catch (error) {
      console.error("Error starting recording:", error);
      const errorMessage = getErrorMessage(error);
      setError(errorMessage);
      setIsRecording(false);
      isRecordingRef.current = false;
      fullCleanup();
      throw new Error(errorMessage);
    }
  }, [
    clearAllTimeouts,
    createMicrophoneStream,
    connectSocket,
    encodePCMChunk,
    fullCleanup,
  ]);

  const stopRecording = useCallback(
    ({ isFinished }: { isFinished?: boolean } = {}) => {
      // Clear any existing timeouts
      clearAllTimeouts();

      // Set recording state to false immediately
      isRecordingRef.current = false;
      setIsRecording(false);

      // For report dictation, add pause indicator if not finished
      if (
        type === "report_dictation" &&
        currentTranscriptRef.current.trim().length > 0 &&
        !isFinished
      ) {
        currentTranscriptRef.current += `\n\n<---------RECORDING PAUSED-------->`;
        setTranscriptions(currentTranscriptRef.current);
      }

      // Clean up resources with a small delay to ensure final transcription data is received
      stopTimeoutRef.current = setTimeout(
        () => {
          fullCleanup();
        },
        isFinished ? 0 : 1000
      ); // Immediate cleanup if finished, otherwise wait 1 second
    },
    [clearAllTimeouts, fullCleanup, type]
  );

  const stopAndClearRecording = useCallback((): void => {
    stopRecording({ isFinished: true });
    currentTranscriptRef.current = "";
    setTranscriptions("");
  }, [stopRecording]);

  // Handle visibility change (iOS Safari specific)
  const handleVisibilityChange = useCallback(() => {
    if (document.hidden && isRecordingRef.current) {
      // Page is hidden, pause recording to prevent issues
      console.log("Page hidden, pausing recording");
      stopRecording();
    }
  }, [stopRecording]);

  // Handle page unload
  const handleBeforeUnload = useCallback(() => {
    if (isRecordingRef.current) {
      fullCleanup();
    }
  }, [fullCleanup]);

  useEffect(() => {
    // Add event listeners for iOS Safari compatibility
    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("beforeunload", handleBeforeUnload);
      fullCleanup();
    };
  }, [handleVisibilityChange, handleBeforeUnload, fullCleanup]);

  useEffect(() => {
    const handleTextareaInput = () => {
      if (textareaRef.current) {
        currentTranscriptRef.current = textareaRef.current.value || "";
        setTranscriptions(currentTranscriptRef.current);
      }
    };

    const textarea = textareaRef.current;
    textarea?.addEventListener("input", handleTextareaInput);

    return () => {
      textarea?.removeEventListener("input", handleTextareaInput);
    };
  }, [textareaRef]);

  return {
    isRecording,
    transcriptions,
    startRecording,
    stopRecording,
    error,
    stopAndClearRecording,
    FINISH_DELAY_TIME,
  };
};

export default useTranscription;
