import { ConfirmModalConfig } from "types";
import { create } from "zustand";
import { persist } from "zustand/middleware";

interface PayloadInterface {
  confirmModalConfig: ConfirmModalConfig;
  entityConfiguration: any;
  allowedFileTypes: string[];
  allStoredEntities: string[];
}

export const DEFAULT_PRESET = "custom1";

const initialState = {
  confirmModalConfig: {
    visible: false,
    data: {},
  },
  entityConfiguration: {
    preset: [
      {
        entity_values: [],
        type: DEFAULT_PRESET,
      },
    ],
    enable_privacy: true,
  },
  allowedFileTypes: [],
  allStoredEntities: [],
  isLoggingOut: false,
  docSettings: {
    font: "",
    primary_logo: "",
    secondary_logo: "",
  },
  tempOrgProfile: {},
};

const useUtilStore = create(
  persist(
    (set: any) => ({
      ...initialState,
      setConfirmModalConfig: (data: ConfirmModalConfig) =>
        set((state: PayloadInterface) => ({
          ...state,
          confirmModalConfig: data,
        })),
      setEntityConfiguration: (data: any) =>
        set((state: PayloadInterface) => ({
          ...state,
          entityConfiguration: data,
        })),
      setAllowedFileTypes: (data: string[]) =>
        set((state: PayloadInterface) => ({
          ...state,
          allowedFileTypes: data,
        })),
      setAllStoredEntities: (data: string[]) =>
        set((state: PayloadInterface) => ({
          ...state,
          allStoredEntities: data,
        })),
      resetEntityConfiguration: () =>
        set((state: PayloadInterface) => ({
          ...state,
          entityConfiguration: initialState.entityConfiguration,
        })),
      setIsLoggingOut: (value: boolean) => set({ isLoggingOut: value }),
      setDocSettings: (data: any) =>
        set((state: PayloadInterface) => ({
          ...state,
          docSettings: data,
        })),
      setTempOrgProfile: (data: any) =>
        set((state: PayloadInterface) => ({
          ...state,
          tempOrgProfile: data,
        })),
    }),
    {
      name: "utils",
      partialize: (state: PayloadInterface) => ({
        entityConfiguration: state.entityConfiguration,
      }),
    },
  ),
);

export const getAllowedFileTypes = () => {
  return useUtilStore.getState().allowedFileTypes;
};

export const getEntityConfiguration = () => {
  return useUtilStore.getState().entityConfiguration;
};

export const getAllStoredEntities = () => {
  return useUtilStore.getState().allStoredEntities;
};

export const getEntitiesBasedOnPreset = (
  preset_type: string = "full",
  storedEntitiesData = [],
) => {
  if (!storedEntitiesData?.length) {
    storedEntitiesData = useUtilStore.getState().allStoredEntities;
  }
  const allEntitiesContent: any =
    storedEntitiesData?.length > 0
      ? storedEntitiesData.map((item: any) => item.entities).flat()
      : [];
  if (preset_type === "basic") {
    return allEntitiesContent
      .filter((item: any) => item?.preset_type === "basic" && !item?.upcoming)
      .map((item: any) => item.value);
  } else {
    return allEntitiesContent
      .filter((item: any) => !item?.upcoming)
      .map((item: any) => item.value);
  }
};

export const {
  setConfirmModalConfig,
  setEntityConfiguration,
  setAllowedFileTypes,
  resetEntityConfiguration,
  setAllStoredEntities,
  setIsLoggingOut,
  setDocSettings,
  setTempOrgProfile,
} = useUtilStore.getState();

export default useUtilStore;
