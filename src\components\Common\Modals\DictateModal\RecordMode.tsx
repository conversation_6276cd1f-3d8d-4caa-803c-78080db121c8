import { RiPauseLine, RiPlayLine, RiResetRightLine } from "@remixicon/react";
import { WaveIcon } from "components/Icons";
import { Button } from "react-bootstrap";
import { isMobile } from "react-device-detect";

interface RecordModeProps {
  config: {
    isRecording: boolean;
    handleToggleRecording: () => void;
    hasStarted: boolean;
    handleReset: () => void;
    handleFinishRecording: () => void;
    hasAnyText: boolean;
    isLoading?: boolean;
  };
}

export default function RecordMode({ config }: RecordModeProps) {
  const {
    isRecording,
    handleToggleRecording,
    hasStarted,
    handleReset,
    handleFinishRecording,
    hasAnyText,
    isLoading,
  } = config;

  return (
    <div className="w-100 d-flex flex-column justify-content-center gap-5 flex-grow-1">
      <div className="d-flex justify-content-center mb-2 flex-column align-items-center gap-4">
        <WaveIcon isActive={isRecording} />
        <h3 className="mb-0 fw-bold">Recording</h3>
      </div>

      <div className="d-flex align-items-center gap-5 flex-wrap justify-content-center">
        <Button
          variant="light"
          onClick={handleToggleRecording}
          title={`${isRecording ? "Pause" : hasStarted ? "Resume" : "Start"} Recording`}
          className={isMobile ? "bg-transparent" : ""}
        >
          {isRecording ? (
            <RiPauseLine size={22} />
          ) : (
            <RiPlayLine size={30} color="#0d3149" />
          )}
        </Button>

        <Button
          variant="light"
          onClick={hasStarted ? handleReset : () => {}}
          title="Restart Recording"
          className={hasStarted ? "" : "opacity-50 bg-light"}
        >
          <RiResetRightLine size={22} color={hasStarted ? "#0d3149" : "#ccc"} />
        </Button>
      </div>

      <div className="w-100 d-flex justify-content-center">
        <Button
          variant=""
          className="bg-brown border-brown text-white fw-bold px-5 py-2"
          onClick={handleFinishRecording}
          disabled={!hasAnyText || isLoading}
        >
          {isLoading ? "Please wait..." : "FINISH"}
        </Button>
      </div>
    </div>
  );
}
