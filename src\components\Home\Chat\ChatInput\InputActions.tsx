import { RiCameraFill } from "@remixicon/react";
import FilePreview from "./FilePreview";
import FileUpload from "./FileUpload";
import SpeechToText from "./SpeechToText";
import { isValidFileType } from "utils";

const InputActions = ({
  selectedFile,
  handleRemoveFile,
  selectedDocID,
  uploadDocChatId,
  handleFileChange,
  fileRef,
  uploadProgress,
  textareaRef,
  transcriptionConfig,
}: any) => {
  const handleImageChange = (file: any) => {
    if (!isValidFileType(file)) {
      return;
    }
    handleFileChange(file);
  };
  return (
    <>
      <FilePreview
        selectedFile={selectedFile}
        handleRemoveFile={handleRemoveFile}
        selectedDocID={selectedDocID}
        uploadDocChatId={uploadDocChatId}
        uploadProgress={uploadProgress}
      />
      <FileUpload
        handleFileChange={handleFileChange}
        fileRef={fileRef}
        controlId="fileInput"
      />

      <FileUpload
        handleFileChange={handleImageChange}
        fileRef={fileRef}
        controlId="camera"
        icon={RiCameraFill}
        customClass="camera"
        accept="image/jpeg, image/png, image/jpg"
      />

      <SpeechToText
        textareaRef={textareaRef}
        transcriptionConfig={transcriptionConfig}
      />
    </>
  );
};

export default InputActions;
