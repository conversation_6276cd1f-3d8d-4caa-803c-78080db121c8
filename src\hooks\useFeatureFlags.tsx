import { useMemo } from "react";

export const ALL_FEATURE_FLAGS = [
  "CLIENT_DATABASE",
  "KNOWLEDGE_BASE",
  "REPORTS",
  "MEETINGS",
] as const;

export type FeatureFlagName = (typeof ALL_FEATURE_FLAGS)[number];

const enabledFlagsRaw = import.meta.env.VITE_FEATURE_FLAGS || "";
const ENABLED_FLAGS_SET = new Set(
  enabledFlagsRaw
    .split(",")
    .map((f: string) => f.trim()?.toLowerCase())
    .filter(Boolean),
);

export default function useFeatureFlags() {
  const isFeatureEnabled = useMemo(() => {
    return (feature: FeatureFlagName): boolean => {
      return ENABLED_FLAGS_SET.has(feature?.toLowerCase());
    };
  }, []);

  return { isFeatureEnabled };
}
