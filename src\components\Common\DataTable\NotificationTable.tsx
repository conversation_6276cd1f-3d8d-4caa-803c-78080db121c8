import { RiDownload2Fill } from "@remixicon/react";
import React from "react";
import { Button, Table } from "react-bootstrap";
import HoverTooltip from "../HoverTooltip";
import CustomPagination from "./CustomPagination";
import "./styles.scss";

interface NotificationTableProps {
  columns: {
    field: string;
    headerName: string;
  }[];
  rows: { [key: string]: any }[];
  paginationProps: any;
  isRowHover?: Record<string, boolean>;
  setIsRowHover?: any;
  onReadNotification?: any;
}

const NotificationTable: React.FC<NotificationTableProps> = ({
  columns,
  rows,
  paginationProps,
  setIsRowHover,
  onReadNotification,
}) => {
  const colSpan = columns.length;

  const onViewFile = (row: any) => {
    window.open(row?.metadata?.preview_url, "_blank");
  };

  return (
    <>
      <Table
        borderless
        hover
        responsive
        className="h-100 notification-custom-table mb-0 align-middle position-relative"
      >
        <thead className="bg-white position-sticky z-2">
          <tr>
            <th colSpan={colSpan}>
              <hr className="mt-0 mb-2 p-0 border-0 w-100 d-block" />
            </th>
          </tr>

          <tr>
            {columns.map((col, index) => (
              <th key={index}>
                <div
                  className="d-flex align-items-center"
                  style={{ gap: "10px" }}
                >
                  <p className="mb-0">{col?.headerName}</p>
                </div>
              </th>
            ))}
          </tr>

          <tr>
            <th colSpan={colSpan}>
              <hr className="mb-0 mt-2 p-0 border-0 w-100 d-block" />
            </th>
          </tr>
        </thead>

        <tbody>
          <tr>
            <td colSpan={colSpan} className="blank-row"></td>
          </tr>

          {rows?.length > 0 &&
            rows.map((row, rowIndex) => (
              <React.Fragment key={rowIndex}>
                <tr
                  className={`${row?.is_read ? "" : "selected-row"}`}
                  onMouseEnter={() => setIsRowHover({ [rowIndex]: true })}
                  onMouseLeave={() => setIsRowHover({ [rowIndex]: false })}
                >
                  {columns.map((col, colIndex) => (
                    <td
                      key={colIndex}
                      className={
                        colIndex === columns.length - 1 ? "align-baseline" : ""
                      }
                    >
                      {colIndex === columns.length - 1 ? (
                        <div className="d-flex align-items-center w-100 position-relative end-row">
                          <p
                            className={colIndex === 0 ? "fw-bold mb-0" : "mb-0"}
                          >
                            {row[col?.field]}
                          </p>
                          {row?.notification_type === "chat_export" && (
                            <div className="ms-2 w-100 text-center">
                              <HoverTooltip
                                title="Download File"
                                customClass="fw-bold"
                              >
                                <Button
                                  className={`action-item-btn bg-brown border-brown text-uppercase font-light`}
                                  onClick={() => onViewFile(row)}
                                >
                                  <RiDownload2Fill size={20} />
                                </Button>
                              </HoverTooltip>
                            </div>
                          )}
                          {!row?.is_read && (
                            <div className="action-btns ms-2">
                              <Button
                                className={`submit-btn w-100 bg-brown border-brown text-uppercase font-light`}
                                onClick={() => onReadNotification(row?.id)}
                              >
                                Mark as read
                              </Button>
                            </div>
                          )}
                        </div>
                      ) : (
                        <p className={colIndex === 0 ? "fw-bold mb-0" : "mb-0"}>
                          {row[col?.field]}
                        </p>
                      )}
                    </td>
                  ))}
                </tr>

                <tr>
                  <td colSpan={colSpan} className="blank-row"></td>
                </tr>
              </React.Fragment>
            ))}
        </tbody>
      </Table>

      {rows?.length > 0 && <CustomPagination {...paginationProps} />}
    </>
  );
};

export default NotificationTable;
